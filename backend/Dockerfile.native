# GraalVM Native Image Dockerfile
FROM ghcr.io/graalvm/graalvm-community:17-ol8 AS builder

# Install native-image
RUN gu install native-image

# Install Maven
RUN microdnf install -y wget tar gzip && \
    wget https://archive.apache.org/dist/maven/maven-3/3.9.6/binaries/apache-maven-3.9.6-bin.tar.gz && \
    tar -xzf apache-maven-3.9.6-bin.tar.gz -C /opt && \
    ln -s /opt/apache-maven-3.9.6 /opt/maven && \
    rm apache-maven-3.9.6-bin.tar.gz

ENV MAVEN_HOME=/opt/maven
ENV PATH=$MAVEN_HOME/bin:$PATH

WORKDIR /app

# Copy pom.xml and download dependencies
COPY pom.xml .
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src

# Build native image with profile
RUN mvn clean -Pnative package -DskipTests

# Runtime stage
FROM oraclelinux:8-slim

# Install runtime dependencies
RUN microdnf update -y && \
    microdnf install -y \
    curl \
    ca-certificates \
    glibc-devel \
    zlib-devel && \
    curl -fsSL https://download.docker.com/linux/centos/docker-ce.repo -o /etc/yum.repos.d/docker-ce.repo && \
    microdnf install -y docker-ce-cli && \
    microdnf clean all

# Create non-root user
RUN groupadd -r mcpproxy && useradd -r -g mcpproxy mcpproxy

WORKDIR /app

# Copy native executable
COPY --from=builder /app/target/spring-vue-app /app/spring-vue-app

# Create data directory
RUN mkdir -p /app/data && chown -R mcpproxy:mcpproxy /app

# Switch to non-root user
USER mcpproxy

EXPOSE 8080

# Health check for native image
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

ENTRYPOINT ["./spring-vue-app"]
