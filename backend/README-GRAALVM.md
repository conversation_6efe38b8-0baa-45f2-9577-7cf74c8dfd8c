# GraalVM Native Image 构建指南

本文档介绍如何使用GraalVM将Spring Boot应用编译为原生镜像。

## 前提条件

### 1. 安装GraalVM

```bash
# 下载并安装GraalVM Community Edition 17
# 方法1: 使用SDKMAN
sdk install java 17.0.9-graalce
sdk use java 17.0.9-graalce

# 方法2: 手动下载
# 从 https://github.com/graalvm/graalvm-ce-builds/releases 下载
```

### 2. 安装Native Image

```bash
# 安装native-image组件
gu install native-image

# 验证安装
native-image --version
```

### 3. 系统依赖

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install build-essential zlib1g-dev
```

#### Linux (CentOS/RHEL)
```bash
sudo yum groupinstall "Development Tools"
sudo yum install zlib-devel
```

#### macOS
```bash
xcode-select --install
```

## 构建方法

### 方法1: 使用Maven Profile

```bash
# 构建原生镜像
mvn clean -Pnative package -DskipTests

# 运行原生镜像
./target/spring-vue-app
```

### 方法2: 使用构建脚本

```bash
# 使用提供的构建脚本
./build-native.sh
```

### 方法3: 使用Docker

```bash
# 构建原生镜像Docker容器
docker build -f Dockerfile.native -t spring-vue-app:native .

# 运行原生镜像容器
docker run -p 8080:8080 spring-vue-app:native
```

### 方法4: 使用Docker Compose

```bash
# 启动原生镜像版本
docker-compose --profile native up backend-native

# 或者同时启动所有服务（包括原生后端）
docker-compose --profile native up
```

## 配置说明

### Maven配置

项目已配置了`native` profile，包含以下关键设置：

- **native-maven-plugin**: GraalVM官方Maven插件
- **主类**: `com.example.springvueapp.SpringVueAppApplication`
- **构建参数**: 针对Spring WebFlux和Docker客户端优化
- **Agent支持**: 启用了GraalVM tracing agent

### GraalVM配置文件

项目包含以下预配置的GraalVM配置文件：

- `reflect-config.json`: 反射配置
- `resource-config.json`: 资源文件配置
- `jni-config.json`: JNI访问配置
- `proxy-config.json`: 动态代理配置
- `serialization-config.json`: 序列化配置

### 关键构建参数

```xml
<buildArgs>
    <buildArg>--no-fallback</buildArg>
    <buildArg>--enable-http</buildArg>
    <buildArg>--enable-https</buildArg>
    <buildArg>--enable-url-protocols=http,https</buildArg>
    <buildArg>--initialize-at-build-time=org.slf4j</buildArg>
    <buildArg>--initialize-at-run-time=io.netty.channel.epoll.Epoll</buildArg>
    <!-- 更多Netty相关的运行时初始化配置 -->
    <buildArg>--initialize-at-run-time=com.github.dockerjava</buildArg>
    <buildArg>--allow-incomplete-classpath</buildArg>
    <buildArg>--report-unsupported-elements-at-runtime</buildArg>
</buildArgs>
```

## 性能对比

### 启动时间
- **JVM版本**: ~3-5秒
- **Native版本**: ~0.1-0.3秒

### 内存使用
- **JVM版本**: ~200-300MB
- **Native版本**: ~50-100MB

### 文件大小
- **JAR文件**: ~50-80MB
- **Native可执行文件**: ~80-120MB

## 故障排除

### 常见问题

#### 1. 构建失败 - 缺少反射配置

**错误信息**:
```
ClassNotFoundException during image generation
```

**解决方案**:
- 检查`reflect-config.json`是否包含所需的类
- 使用GraalVM agent收集配置：
```bash
mvn -Pnative -Dagent=true test
```

#### 2. 运行时错误 - 资源文件未找到

**错误信息**:
```
Resource not found: application.properties
```

**解决方案**:
- 检查`resource-config.json`配置
- 确保资源文件路径正确

#### 3. Docker构建失败

**错误信息**:
```
native-image command not found
```

**解决方案**:
- 确保Dockerfile使用正确的GraalVM基础镜像
- 检查`gu install native-image`是否成功执行

### 调试技巧

#### 1. 启用详细输出
```bash
mvn -Pnative package -DskipTests -Dverbose=true
```

#### 2. 生成构建报告
```bash
mvn -Pnative package -DskipTests -Dargs="--emit build-report"
```

#### 3. 使用Agent收集配置
```bash
# 运行测试并收集配置
mvn -Pnative -Dagent=true test

# 复制生成的配置
mvn -Pnative -Dagent=true test native:metadata-copy
```

## 部署建议

### 生产环境

1. **使用原生镜像**用于：
   - 容器化部署
   - 无服务器函数
   - 资源受限环境

2. **使用JVM版本**用于：
   - 开发和调试
   - 需要动态类加载的场景
   - 复杂的反射使用

### 监控和观察

原生镜像支持以下监控功能：
- Spring Boot Actuator
- Micrometer metrics
- 自定义健康检查

## 参考资源

- [GraalVM官方文档](https://www.graalvm.org/latest/reference-manual/native-image/)
- [Spring Boot Native文档](https://docs.spring.io/spring-boot/docs/current/reference/html/native-image.html)
- [GraalVM Native Build Tools](https://graalvm.github.io/native-build-tools/latest/index.html)
