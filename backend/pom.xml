<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.0</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.example</groupId>
    <artifactId>spring-vue-app</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>spring-vue-app</name>
    <description>Spring Boot and Vue 3 Application</description>

    <properties>
        <java.version>17</java.version>
        <native.maven.plugin.version>0.10.3</native.maven.plugin.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-r2dbc</artifactId>
        </dependency>
        <dependency>
            <groupId>io.r2dbc</groupId>
            <artifactId>r2dbc-h2</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!-- JDBC 支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.11.5</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.11.5</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>0.11.5</version>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Docker Java Client for sandbox management -->
        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java-core</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java-transport-httpclient5</artifactId>
            <version>3.5.1</version>
        </dependency>

        <!-- Jackson for JSON processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>

        <!-- Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>native</id>
            <properties>
                <repackage.classifier/>
                <native-buildtools.version>${native.maven.plugin.version}</native-buildtools.version>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>org.junit.platform</groupId>
                    <artifactId>junit-platform-launcher</artifactId>
                    <scope>test</scope>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.graalvm.buildtools</groupId>
                        <artifactId>native-maven-plugin</artifactId>
                        <version>${native.maven.plugin.version}</version>
                        <extensions>true</extensions>
                        <executions>
                            <execution>
                                <id>test-native</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>build-native</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>compile-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <imageName>${project.artifactId}</imageName>
                            <mainClass>com.example.springvueapp.SpringVueAppApplication</mainClass>
                            <fallback>false</fallback>
                            <verbose>true</verbose>
                            <buildArgs>
                                <buildArg>--no-fallback</buildArg>
                                <buildArg>--enable-http</buildArg>
                                <buildArg>--enable-https</buildArg>
                                <buildArg>--enable-url-protocols=http,https</buildArg>
                                <buildArg>--initialize-at-build-time=org.slf4j</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.channel.epoll.Epoll</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.channel.epoll.Native</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.channel.epoll.EpollEventLoop</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.channel.epoll.EpollEventArray</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.channel.kqueue.KQueue</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.channel.kqueue.KQueueEventLoop</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.channel.kqueue.KQueueEventArray</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.channel.kqueue.Native</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.channel.unix.Errors</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.channel.unix.IovArray</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.channel.unix.Limits</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.util.internal.logging.Log4JLogger</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.handler.codec.http2.Http2CodecUtil</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.handler.codec.http2.DefaultHttp2FrameWriter</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.handler.ssl.BouncyCastleAlpnSslUtils</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.handler.ssl.ConscryptAlpnSslEngine</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.handler.ssl.JettyNpnSslEngine</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.handler.ssl.ReferenceCountedOpenSslEngine</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.handler.ssl.JdkNpnApplicationProtocolNegotiator</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.handler.ssl.ReferenceCountedOpenSslServerContext</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.handler.ssl.ReferenceCountedOpenSslClientContext</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.handler.ssl.util.ThreadLocalInsecureRandom</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty.handler.ssl.ReferenceCountedOpenSslContext</buildArg>
                                <buildArg>--initialize-at-run-time=com.github.dockerjava</buildArg>
                                <buildArg>--initialize-at-run-time=org.apache.commons.compress</buildArg>
                                <buildArg>--allow-incomplete-classpath</buildArg>
                                <buildArg>--report-unsupported-elements-at-runtime</buildArg>
                            </buildArgs>
                            <agent>
                                <enabled>true</enabled>
                            </agent>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>3.0.0</version>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
