package com.example.springvueapp.sandbox;

import com.example.springvueapp.model.HostEntry;
import com.example.springvueapp.model.VolumeMount;

import java.util.List;
import java.util.Map;

/**
 * 创建沙箱实例的配置
 */
public class SandboxConfig {

    private String id;
    private String name;
    private String description;
    private String dockerImage;
    private String command;
    private List<String> arguments;
    private Map<String, String> environment;
    private String workingDirectory;
    private ResourceLimits resourceLimits;
    private NetworkConfig networkConfig;
    private List<VolumeMount> volumeMounts;
    private List<HostEntry> hostEntries;
    private Integer timeoutSeconds;
    private Boolean autoRestart = false;

    public SandboxConfig() {
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDockerImage() {
        return dockerImage;
    }

    public void setDockerImage(String dockerImage) {
        this.dockerImage = dockerImage;
    }

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public List<String> getArguments() {
        return arguments;
    }

    public void setArguments(List<String> arguments) {
        this.arguments = arguments;
    }

    public Map<String, String> getEnvironment() {
        return environment;
    }

    public void setEnvironment(Map<String, String> environment) {
        this.environment = environment;
    }

    public String getWorkingDirectory() {
        return workingDirectory;
    }

    public void setWorkingDirectory(String workingDirectory) {
        this.workingDirectory = workingDirectory;
    }

    public ResourceLimits getResourceLimits() {
        return resourceLimits;
    }

    public void setResourceLimits(ResourceLimits resourceLimits) {
        this.resourceLimits = resourceLimits;
    }

    public NetworkConfig getNetworkConfig() {
        return networkConfig;
    }

    public void setNetworkConfig(NetworkConfig networkConfig) {
        this.networkConfig = networkConfig;
    }

    public List<VolumeMount> getVolumeMounts() {
        return volumeMounts;
    }

    public void setVolumeMounts(List<VolumeMount> volumeMounts) {
        this.volumeMounts = volumeMounts;
    }

    public List<HostEntry> getHostEntries() {
        return hostEntries;
    }

    public void setHostEntries(List<HostEntry> hostEntries) {
        this.hostEntries = hostEntries;
    }

    public Integer getTimeoutSeconds() {
        return timeoutSeconds;
    }

    public void setTimeoutSeconds(Integer timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }

    public Boolean getAutoRestart() {
        return autoRestart;
    }

    public void setAutoRestart(Boolean autoRestart) {
        this.autoRestart = autoRestart;
    }

    public static class ResourceLimits {
        private Long memoryLimitBytes;
        private Double cpuLimit;
        private Long diskLimitBytes;

        public ResourceLimits() {
        }

        public Long getMemoryLimitBytes() {
            return memoryLimitBytes;
        }

        public void setMemoryLimitBytes(Long memoryLimitBytes) {
            this.memoryLimitBytes = memoryLimitBytes;
        }

        public Double getCpuLimit() {
            return cpuLimit;
        }

        public void setCpuLimit(Double cpuLimit) {
            this.cpuLimit = cpuLimit;
        }

        public Long getDiskLimitBytes() {
            return diskLimitBytes;
        }

        public void setDiskLimitBytes(Long diskLimitBytes) {
            this.diskLimitBytes = diskLimitBytes;
        }
    }

    public static class NetworkConfig {
        private Boolean enableNetworking;
        private List<String> allowedHosts;
        private List<Integer> exposedPorts;

        public NetworkConfig() {
        }

        public Boolean getEnableNetworking() {
            return enableNetworking;
        }

        public void setEnableNetworking(Boolean enableNetworking) {
            this.enableNetworking = enableNetworking;
        }

        public List<String> getAllowedHosts() {
            return allowedHosts;
        }

        public void setAllowedHosts(List<String> allowedHosts) {
            this.allowedHosts = allowedHosts;
        }

        public List<Integer> getExposedPorts() {
            return exposedPorts;
        }

        public void setExposedPorts(List<Integer> exposedPorts) {
            this.exposedPorts = exposedPorts;
        }
    }

    /**
     * SandboxConfig 的构建器类
     */
    public static class Builder {
        private final SandboxConfig config = new SandboxConfig();

        public Builder id(String id) {
            config.setId(id);
            return this;
        }

        public Builder name(String name) {
            config.setName(name);
            return this;
        }

        public Builder description(String description) {
            config.setDescription(description);
            return this;
        }

        public Builder dockerImage(String dockerImage) {
            config.setDockerImage(dockerImage);
            return this;
        }

        public Builder command(String command) {
            config.setCommand(command);
            return this;
        }

        public Builder arguments(List<String> arguments) {
            config.setArguments(arguments);
            return this;
        }

        public Builder environment(Map<String, String> environment) {
            config.setEnvironment(environment);
            return this;
        }

        public Builder workingDirectory(String workingDirectory) {
            config.setWorkingDirectory(workingDirectory);
            return this;
        }

        public Builder resourceLimits(ResourceLimits resourceLimits) {
            config.setResourceLimits(resourceLimits);
            return this;
        }

        public Builder networkConfig(NetworkConfig networkConfig) {
            config.setNetworkConfig(networkConfig);
            return this;
        }

        public Builder volumeMounts(List<VolumeMount> volumeMounts) {
            config.setVolumeMounts(volumeMounts);
            return this;
        }

        public Builder hostEntries(List<HostEntry> hostEntries) {
            config.setHostEntries(hostEntries);
            return this;
        }

        public Builder timeoutSeconds(Integer timeoutSeconds) {
            config.setTimeoutSeconds(timeoutSeconds);
            return this;
        }

        public Builder autoRestart(Boolean autoRestart) {
            config.setAutoRestart(autoRestart);
            return this;
        }

        public SandboxConfig build() {
            return config;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
