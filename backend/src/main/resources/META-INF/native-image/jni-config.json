[{"name": "java.lang.System", "methods": [{"name": "getProperty", "parameterTypes": ["java.lang.String"]}, {"name": "getProperty", "parameterTypes": ["java.lang.String", "java.lang.String"]}, {"name": "setProperty", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"name": "java.lang.Runtime", "methods": [{"name": "exec", "parameterTypes": ["java.lang.String"]}, {"name": "exec", "parameterTypes": ["java.lang.String[]"]}]}, {"name": "java.lang.ProcessBuilder", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "java.lang.Process", "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "java.io.File", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "java.nio.file.Path", "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "java.nio.file.Paths", "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "java.net.Socket", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "java.net.ServerSocket", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "java.net.InetAddress", "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "java.net.NetworkInterface", "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "sun.nio.ch.EPollSelectorImpl", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "sun.nio.ch.K<PERSON>ueueSelectorImpl", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}]