[{"interfaces": ["org.springframework.data.repository.Repository", "org.springframework.data.repository.reactive.ReactiveCrudRepository"]}, {"interfaces": ["org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["org.springframework.transaction.annotation.Transactional"]}, {"interfaces": ["org.springframework.web.bind.annotation.RestController"]}, {"interfaces": ["org.springframework.stereotype.Service"]}, {"interfaces": ["org.springframework.stereotype.Component"]}, {"interfaces": ["org.springframework.context.annotation.Configuration"]}, {"interfaces": ["reactor.core.publisher.Mono", "reactor.core.publisher.Flux"]}, {"interfaces": ["com.fasterxml.jackson.databind.JsonSerializer", "com.fasterxml.jackson.databind.JsonDeserializer"]}, {"interfaces": ["org.springframework.security.core.userdetails.UserDetails"]}, {"interfaces": ["org.springframework.security.core.Authentication"]}, {"interfaces": ["com.github.dockerjava.api.DockerClient"]}, {"interfaces": ["com.github.dockerjava.api.command.CreateContainerCmd", "com.github.dockerjava.api.command.StartContainerCmd", "com.github.dockerjava.api.command.StopContainerCmd", "com.github.dockerjava.api.command.RemoveContainerCmd"]}]