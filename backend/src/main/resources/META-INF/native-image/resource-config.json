{"resources": {"includes": [{"pattern": "\\Qapplication.properties\\E"}, {"pattern": "\\Qapplication-*.properties\\E"}, {"pattern": "\\Qapplication.yml\\E"}, {"pattern": "\\Qapplication-*.yml\\E"}, {"pattern": "\\Qschema.sql\\E"}, {"pattern": "\\Qdata.sql\\E"}, {"pattern": "\\QMETA-INF/spring.factories\\E"}, {"pattern": "\\QMETA-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports\\E"}, {"pattern": "\\QMETA-INF/spring/org.springframework.boot.actuate.autoconfigure.web.ManagementContextConfiguration.imports\\E"}, {"pattern": "\\QMETA-INF/spring-configuration-metadata.json\\E"}, {"pattern": "\\QMETA-INF/additional-spring-configuration-metadata.json\\E"}, {"pattern": "\\Qorg/springframework/.*\\E"}, {"pattern": "\\Qorg/h2/.*\\E"}, {"pattern": "\\Qio/netty/.*\\E"}, {"pattern": "\\Qreactor/.*\\E"}, {"pattern": "\\Qcom/fasterxml/jackson/.*\\E"}, {"pattern": "\\Qio/jsonwebtoken/.*\\E"}, {"pattern": "\\Qcom/github/dockerjava/.*\\E"}, {"pattern": "\\Qorg/apache/commons/.*\\E"}, {"pattern": "\\Qorg/apache/http/.*\\E"}, {"pattern": "\\Qorg/slf4j/.*\\E"}, {"pattern": "\\Qch/qos/logback/.*\\E"}, {"pattern": "\\Qlogback.xml\\E"}, {"pattern": "\\Qlogback-spring.xml\\E"}, {"pattern": "\\Qlog4j2.xml\\E"}, {"pattern": "\\Qlog4j2-spring.xml\\E"}, {"pattern": "\\Qbanner.txt\\E"}, {"pattern": "\\Qstatic/.*\\E"}, {"pattern": "\\Qtemplates/.*\\E"}, {"pattern": "\\Qpublic/.*\\E"}, {"pattern": "\\Qresources/.*\\E"}, {"pattern": "\\QMETA-INF/services/.*\\E"}, {"pattern": "\\QMETA-INF/native-image/.*\\E"}]}, "bundles": []}