[{"name": "com.example.springvueapp.model.User"}, {"name": "com.example.springvueapp.model.McpServerConfiguration"}, {"name": "com.example.springvueapp.model.McpServerInstance"}, {"name": "com.example.springvueapp.mcp.model.McpRequest"}, {"name": "com.example.springvueapp.mcp.model.McpResponse"}, {"name": "com.example.springvueapp.mcp.model.McpTool"}, {"name": "com.example.springvueapp.mcp.model.McpResource"}, {"name": "com.example.springvueapp.mcp.model.McpPrompt"}, {"name": "com.example.springvueapp.sandbox.SandboxResourceUsage"}, {"name": "java.util.HashMap"}, {"name": "java.util.ArrayList"}, {"name": "java.util.LinkedHashMap"}, {"name": "java.lang.String"}, {"name": "java.lang.Integer"}, {"name": "java.lang.Long"}, {"name": "java.lang.Bo<PERSON>an"}, {"name": "java.lang.Double"}, {"name": "java.time.LocalDateTime"}, {"name": "java.time.Instant"}, {"name": "com.fasterxml.jackson.databind.node.ObjectNode"}, {"name": "com.fasterxml.jackson.databind.node.ArrayNode"}, {"name": "com.fasterxml.jackson.databind.node.TextNode"}, {"name": "com.fasterxml.jackson.databind.node.IntNode"}, {"name": "com.fasterxml.jackson.databind.node.LongNode"}, {"name": "com.fasterxml.jackson.databind.node.BooleanNode"}, {"name": "com.fasterxml.jackson.databind.node.DoubleNode"}, {"name": "com.fasterxml.jackson.databind.node.NullNode"}]