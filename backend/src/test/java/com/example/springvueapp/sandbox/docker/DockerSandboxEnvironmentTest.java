package com.example.springvueapp.sandbox.docker;

import com.example.springvueapp.model.HostEntry;
import com.example.springvueapp.model.VolumeMount;
import com.example.springvueapp.sandbox.SandboxConfig;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Docker沙箱环境的单元测试
 * 重点测试Volume挂载和Host域名解析配置的数据模型
 */
class DockerSandboxEnvironmentTest {

    @Test
    void testSandboxConfigWithVolumeMounts() {
        // 创建Volume挂载配置
        VolumeMount mount1 = new VolumeMount();
        mount1.setHostPath("/host/data");
        mount1.setContainerPath("/container/data");
        mount1.setReadOnly(false);

        VolumeMount mount2 = new VolumeMount();
        mount2.setHostPath("/host/logs");
        mount2.setContainerPath("/container/logs");
        mount2.setReadOnly(true);

        List<VolumeMount> volumeMounts = Arrays.asList(mount1, mount2);

        // 使用Builder创建配置
        SandboxConfig config = SandboxConfig.builder()
                .name("test-sandbox")
                .dockerImage("ubuntu:20.04")
                .command("/bin/bash")
                .volumeMounts(volumeMounts)
                .build();

        // 验证配置
        assertNotNull(config);
        assertEquals("test-sandbox", config.getName());
        assertEquals("ubuntu:20.04", config.getDockerImage());
        assertEquals("/bin/bash", config.getCommand());
        
        List<VolumeMount> actualMounts = config.getVolumeMounts();
        assertNotNull(actualMounts);
        assertEquals(2, actualMounts.size());
        
        // 验证第一个Volume挂载
        VolumeMount actualMount1 = actualMounts.get(0);
        assertEquals("/host/data", actualMount1.getHostPath());
        assertEquals("/container/data", actualMount1.getContainerPath());
        assertFalse(actualMount1.getReadOnly());
        
        // 验证第二个Volume挂载
        VolumeMount actualMount2 = actualMounts.get(1);
        assertEquals("/host/logs", actualMount2.getHostPath());
        assertEquals("/container/logs", actualMount2.getContainerPath());
        assertTrue(actualMount2.getReadOnly());
    }

    @Test
    void testSandboxConfigWithHostEntries() {
        // 创建Host域名解析配置
        HostEntry entry1 = new HostEntry();
        entry1.setHostname("example.com");
        entry1.setIpAddress("***********00");

        HostEntry entry2 = new HostEntry();
        entry2.setHostname("api.example.com");
        entry2.setIpAddress("***********01");

        List<HostEntry> hostEntries = Arrays.asList(entry1, entry2);

        // 使用Builder创建配置
        SandboxConfig config = SandboxConfig.builder()
                .name("test-sandbox")
                .dockerImage("ubuntu:20.04")
                .command("/bin/bash")
                .hostEntries(hostEntries)
                .build();

        // 验证配置
        assertNotNull(config);
        assertEquals("test-sandbox", config.getName());
        
        List<HostEntry> actualEntries = config.getHostEntries();
        assertNotNull(actualEntries);
        assertEquals(2, actualEntries.size());
        
        // 验证第一个Host条目
        HostEntry actualEntry1 = actualEntries.get(0);
        assertEquals("example.com", actualEntry1.getHostname());
        assertEquals("***********00", actualEntry1.getIpAddress());
        
        // 验证第二个Host条目
        HostEntry actualEntry2 = actualEntries.get(1);
        assertEquals("api.example.com", actualEntry2.getHostname());
        assertEquals("***********01", actualEntry2.getIpAddress());
    }

    @Test
    void testSandboxConfigWithBothVolumeAndHost() {
        // 创建完整的配置
        VolumeMount volumeMount = new VolumeMount();
        volumeMount.setHostPath("/host/data");
        volumeMount.setContainerPath("/container/data");
        volumeMount.setReadOnly(false);

        HostEntry hostEntry = new HostEntry();
        hostEntry.setHostname("database.local");
        hostEntry.setIpAddress("********");

        // 使用Builder创建完整配置
        SandboxConfig config = SandboxConfig.builder()
                .name("complete-sandbox")
                .dockerImage("ubuntu:20.04")
                .command("/bin/bash")
                .volumeMounts(Arrays.asList(volumeMount))
                .hostEntries(Arrays.asList(hostEntry))
                .build();

        // 验证所有配置
        assertNotNull(config);
        assertEquals("complete-sandbox", config.getName());
        assertEquals("ubuntu:20.04", config.getDockerImage());
        assertEquals("/bin/bash", config.getCommand());

        // 验证Volume挂载
        assertNotNull(config.getVolumeMounts());
        assertEquals(1, config.getVolumeMounts().size());
        VolumeMount actualMount = config.getVolumeMounts().get(0);
        assertEquals("/host/data", actualMount.getHostPath());
        assertEquals("/container/data", actualMount.getContainerPath());
        assertFalse(actualMount.getReadOnly());

        // 验证Host条目
        assertNotNull(config.getHostEntries());
        assertEquals(1, config.getHostEntries().size());
        HostEntry actualEntry = config.getHostEntries().get(0);
        assertEquals("database.local", actualEntry.getHostname());
        assertEquals("********", actualEntry.getIpAddress());
    }

    @Test
    void testHostEntryConstructors() {
        // 测试默认构造函数
        HostEntry entry1 = new HostEntry();
        assertNull(entry1.getHostname());
        assertNull(entry1.getIpAddress());

        // 测试带参数的构造函数
        HostEntry entry2 = new HostEntry("test.com", "*******");
        assertEquals("test.com", entry2.getHostname());
        assertEquals("*******", entry2.getIpAddress());

        // 测试setter方法
        entry1.setHostname("example.org");
        entry1.setIpAddress("*******");

        assertEquals("example.org", entry1.getHostname());
        assertEquals("*******", entry1.getIpAddress());
    }

    @Test
    void testVolumeAndHostWithNullValues() {
        // 测试Builder处理null值
        SandboxConfig config = SandboxConfig.builder()
                .name("test-sandbox")
                .dockerImage("ubuntu:20.04")
                .volumeMounts(null)
                .hostEntries(null)
                .build();

        assertNotNull(config);
        assertEquals("test-sandbox", config.getName());
        assertEquals("ubuntu:20.04", config.getDockerImage());
        assertNull(config.getVolumeMounts());
        assertNull(config.getHostEntries());
    }

    @Test
    void testVolumeAndHostSettersAndGetters() {
        // 测试直接使用setter和getter
        SandboxConfig config = new SandboxConfig();
        
        VolumeMount mount = new VolumeMount();
        mount.setHostPath("/test");
        mount.setContainerPath("/test");
        
        HostEntry entry = new HostEntry();
        entry.setHostname("test.local");
        entry.setIpAddress("127.0.0.1");
        
        config.setVolumeMounts(Arrays.asList(mount));
        config.setHostEntries(Arrays.asList(entry));

        assertNotNull(config.getVolumeMounts());
        assertEquals(1, config.getVolumeMounts().size());
        assertEquals("/test", config.getVolumeMounts().get(0).getHostPath());

        assertNotNull(config.getHostEntries());
        assertEquals(1, config.getHostEntries().size());
        assertEquals("test.local", config.getHostEntries().get(0).getHostname());
    }

    @Test
    void testDockerSandboxEnvironmentCreation() {
        // 测试DockerSandboxEnvironment能够正确实例化
        DockerSandboxEnvironment environment = new DockerSandboxEnvironment();
        assertNotNull(environment);
    }

    @Test
    void testHostEntryValidation() {
        // 测试Host条目的基本验证
        HostEntry entry = new HostEntry("valid.hostname.com", "***********");
        
        assertNotNull(entry.getHostname());
        assertNotNull(entry.getIpAddress());
        assertFalse(entry.getHostname().isEmpty());
        assertFalse(entry.getIpAddress().isEmpty());
        
        // 验证hostname格式
        assertTrue(entry.getHostname().contains("."));
        
        // 验证IP地址格式（简单检查）
        String[] ipParts = entry.getIpAddress().split("\\.");
        assertEquals(4, ipParts.length);
    }

    @Test
    void testVolumePathValidation() {
        // 测试Volume路径的基本验证
        VolumeMount mount = new VolumeMount();
        mount.setHostPath("/valid/host/path");
        mount.setContainerPath("/valid/container/path");
        mount.setReadOnly(true);
        
        assertNotNull(mount.getHostPath());
        assertNotNull(mount.getContainerPath());
        assertNotNull(mount.getReadOnly());
        
        // 验证路径格式
        assertTrue(mount.getHostPath().startsWith("/"));
        assertTrue(mount.getContainerPath().startsWith("/"));
        assertTrue(mount.getReadOnly());
    }
}
