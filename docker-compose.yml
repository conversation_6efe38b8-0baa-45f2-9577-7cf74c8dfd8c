version: '3.8'

services:
  # Backend Spring Boot Application (JVM)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: jvm-runtime
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_R2DBC_URL=r2dbc:postgresql://postgres:5432/mcpproxy
      - SPRING_R2DBC_USERNAME=mcpproxy
      - SPRING_R2DBC_PASSWORD=mcpproxy123
      - SPRING_DATASOURCE_URL=****************************************
      - SPRING_DATASOURCE_USERNAME=mcpproxy
      - SPRING_DATASOURCE_PASSWORD=mcpproxy123
      - MCP_SANDBOX_DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - mcp_data:/app/data
    depends_on:
      - postgres
    networks:
      - mcp-network
    restart: unless-stopped

  # Backend Spring Boot Application (Native)
  backend-native:
    build:
      context: ./backend
      dockerfile: Dockerfile.native
    ports:
      - "8081:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_R2DBC_URL=r2dbc:postgresql://postgres:5432/mcpproxy
      - SPRING_R2DBC_USERNAME=mcpproxy
      - SPRING_R2DBC_PASSWORD=mcpproxy123
      - SPRING_DATASOURCE_URL=****************************************
      - SPRING_DATASOURCE_USERNAME=mcpproxy
      - SPRING_DATASOURCE_PASSWORD=mcpproxy123
      - MCP_SANDBOX_DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - mcp_data:/app/data
    depends_on:
      - postgres
    networks:
      - mcp-network
    restart: unless-stopped
    profiles:
      - native

  # Frontend Vue.js Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    environment:
      - VITE_API_BASE_URL=http://localhost:8080
    depends_on:
      - backend
    networks:
      - mcp-network
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=mcpproxy
      - POSTGRES_USER=mcpproxy
      - POSTGRES_PASSWORD=mcpproxy123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/main/resources/schema-postgres.sql:/docker-entrypoint-initdb.d/01-schema.sql
    ports:
      - "5432:5432"
    networks:
      - mcp-network
    restart: unless-stopped

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mcp-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - mcp-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  mcp_data:
    driver: local

networks:
  mcp-network:
    driver: bridge
